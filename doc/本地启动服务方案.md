# JH-Loan-Cash 系统本地启动服务方案

## 📋 系统架构概述

JH-Loan-Cash 是一个完整的微服务借贷系统，包含以下主要服务：

### 🎯 核心业务服务
| 服务名 | 端口 | 服务标识 | Apollo App ID | 说明 |
|--------|------|----------|---------------|------|
| **cash-flow** | 8001 | cash-flow | loan-cash-flow-paipai | 主业务流程服务 |
| **capital-core** | 8001 | capital-core-service | capital-core | 资金核心服务 |
| **capital-batch** | 8100 | fin-batch | capital-batch | 批处理任务服务 |
| **cash-manage** | - | cash-manage | cash-flow-manage | 后台管理服务 |
| **sing-service** | 8081 | sing-service | sing-service | 签章服务 |

## 🏗️ 中间件使用分析

### 公用服务 (团队共享，不需要本地部署)
| 中间件 | 用途 | 使用场景 | 风险评估 |
|--------|------|----------|----------|
| **MySQL** | 关系型数据库 | 业务数据存储 | ✅ 可共享 |
| **Redis** | 缓存/分布式锁 | 缓存、分布式锁 | ✅ 可共享 |
| **Apollo** | 配置中心 | 动态配置管理 | ✅ 可共享 |
| **Eureka** | 服务注册发现 | 微服务通信 | ⚠️ **需要隔离** |

### 本地启动服务 (需要在本地启动)
| 服务  | 必需性 | 说明   |
|------|------|------|
| **RabbitMQ** | 必需 | 消息队列 |

### 可选服务 (按业务需要)
| 中间件 | 用途 | 何时需要 | 部署方式 |
|--------|------|----------|----------|
| **XXL-Job** | 分布式任务调度 | capital-batch需要执行定时任务时 | 公用或本地 |
| **SFTP服务器** | 文件传输 | 业务功能涉及文件上传下载时 | 公用 |

## 🚨 关键问题分析

### RabbitMQ 隔离问题
**问题**: 系统大量使用MQ，包含以下关键队列：
- `risk.apply` - 风控申请
- `credit.apply` - 授信申请
- `loan.apply` - 放款申请
- `repay.*` - 还款相关
- `sms.send` - 短信发送

**风险**: 如果多个开发者连接同一个RabbitMQ，会导致：
- 消息被其他开发者消费
- 业务流程被打断
- 调试困难

### Eureka 隔离分析
**服务调用关系**:
- `cash-flow` → `capital-core-service`
- `cash-flow` → `sing-service`
- `cash-manage` → `cash-flow`

**隔离可行性**: Eureka可以公用，因为：
- 服务名称不同，天然隔离
- 本地端口不同，不会冲突
- OpenFeign通过服务名+负载均衡调用，只会路由到对应服务

## 🚀 本地启动方案

### 中间件部署策略
| 中间件 | 部署方式 | 隔离方式 | 说明 |
|--------|----------|----------|------|
| **MySQL** | 可公用 | 数据库名隔离 | 如: capital_core_dev_张三 |
| **Redis** | 可公用 | key前缀隔离 | 如: dev-张三: |
| **Apollo** | 公用 | 应用ID已隔离 | 无需修改 |
| **RabbitMQ** | 本地部署 | 完全隔离 | 避免消息冲突 |
| **Eureka** | 公用 | Zone隔离 | 通过zone实现隔离 |

### 本地启动服务
- **capital-core** (8001) - 必需

## 🎯 启动步骤

### 第一步：安装启动本地RabbitMQ
安装RabbitMQ：https://cloud.tencent.com/developer/article/2303666

管理地址：http://127.0.0.1:15672  
账号：guest  
密码：guest

### 问题：
1. 

### 第二步：配置Apollo Zone隔离
在Apollo配置中心为每个开发者添加不同的Zone配置：

**应用: capital-core (开发者A - 张三)**
```properties
# RabbitMQ配置 (本地)
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin123

# Eureka Zone隔离配置 (公用Eureka)
eureka.client.service-url.defaultZone=http://dev环境Eureka地址:8761/eureka/
eureka.instance.metadata-map.zone=dev-zhangsan
eureka.client.prefer-same-zone-eureka=true
eureka.instance.instance-id=${spring.application.name}:dev-zhangsan:${server.port}
```

**应用: capital-core (开发者B - 李四)**
```properties
# RabbitMQ配置 (本地)
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=admin123

# Eureka Zone隔离配置 (公用Eureka)
eureka.client.service-url.defaultZone=http://dev环境Eureka地址:8761/eureka/
eureka.instance.metadata-map.zone=dev-lisi
eureka.client.prefer-same-zone-eureka=true
eureka.instance.instance-id=${spring.application.name}:dev-lisi:${server.port}
```

### 第三步：启动应用服务

### Zone隔离工作原理

#### 1. 服务注册
每个开发者的服务会注册到Eureka，带有不同的zone标识：
```
capital-core-service:dev-zhangsan:8001 (zone: dev-zhangsan)
capital-core-service:dev-lisi:8001 (zone: dev-lisi)
```

#### 2. 服务发现
当OpenFeign调用服务时：
- 优先查找同zone的服务实例
- 如果同zone没有实例，才会跨zone调用
- 确保开发者A的请求优先路由到开发者A的服务

#### 3. 隔离效果
- **张三的cash-flow** → **张三的capital-core** (同zone优先)
- **李四的cash-flow** → **李四的capital-core** (同zone优先)
- 有效避免服务调用混乱

## 📝 检查清单

### 基础环境
- [ ] Java 17 已安装
- [ ] Maven 3.6+ 已配置
- [ ] Docker 已安装 (用于启动RabbitMQ)

### 中间件服务
- [ ] **RabbitMQ 本地启动成功** (端口5672/15672) - 必需本地部署
- [ ] **MySQL 连接正常** (可公用或本地)
- [ ] **Redis 连接正常** (可公用或本地)
- [ ] **Eureka 连接正常** (公用) - 服务注册中心
- [ ] **Apollo 配置中心可访问** (公用) - 配置管理

### 应用服务启动
- [ ] capital-core 启动成功 (端口8001)
- [ ] capital-batch 启动成功 (端口8100，可选)
- [ ] 服务注册到Eureka成功
- [ ] 健康检查正常: http://localhost:8001/actuator/health

### 配置验证
- [ ] Apollo中已配置本地RabbitMQ连接信息
- [ ] Apollo中已配置数据库连接信息 (本地或公用)
- [ ] Apollo中已配置Redis连接信息 (本地或公用)
- [ ] Apollo中已配置公用Eureka地址

## ⚠️ 重要提醒

### 🔴 必须本地部署
- **RabbitMQ**: 避免消息被其他开发者消费，业务流程冲突

### 🟡 公用但需要Zone隔离
- **Eureka**: 使用Zone隔离，确保服务调用路由正确

### 🟢 可以直接公用
- **MySQL**: 通过不同数据库名隔离 (如: `capital_core_dev_张三`)
- **Redis**: 通过key前缀隔离 (如: `dev-张三:`)
- **Apollo**: 应用ID已经做了隔离

## 🔍 服务验证

启动完成后，访问以下地址验证服务状态：

- **RabbitMQ管理界面**: http://localhost:15672 (admin/admin123)
- **capital-core健康检查**: http://localhost:8001/actuator/health
- **capital-batch健康检查**: http://localhost:8100/actuator/health (如果启动)
- **Eureka服务列表**: http://dev环境Eureka地址:8761 (确认zone隔离生效)

## 💡 方案优势

### 🎯 资源优化
- **只需启动RabbitMQ**: 最小化本地资源占用
- **Eureka公用**: 减少维护成本，Zone隔离确保安全

### 🛡️ 有效隔离
- **消息队列完全隔离**: 避免业务流程干扰
- **服务调用Zone隔离**: 同zone优先，确保路由正确
- **数据存储可选隔离**: 根据需要选择本地或公用

### ⚡ 快速启动
```bash
# 1. 启动RabbitMQ
docker run -d --name rabbitmq-local -p 5672:5672 -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin -e RABBITMQ_DEFAULT_PASS=admin123 \
  rabbitmq:3.8-management

# 2. 在Apollo中配置Zone隔离 (每个开发者不同的zone名)

# 3. 启动应用
cd capital-core && mvn spring-boot:run
```

## 🔍 服务验证

启动完成后，访问以下地址验证服务状态：

- **RabbitMQ管理界面**: http://localhost:15672 (admin/admin123)
- **capital-core健康检查**: http://localhost:8001/actuator/health

## 💡 方案优势

### 🎯 最小化部署
- **只需启动RabbitMQ**: 大幅节省本地资源
- **其他中间件公用**: 减少维护成本
- **配置简单**: 无需复杂的隔离配置

### 🛡️ 有效隔离
- **消息队列完全隔离**: 避免业务流程干扰
- **服务调用天然隔离**: 服务名+端口确保路由正确
- **数据存储可选隔离**: 根据需要选择本地或公用

### ⚡ 快速启动
```bash
# 1. 启动RabbitMQ
docker run -d --name rabbitmq-local -p 5672:5672 -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin -e RABBITMQ_DEFAULT_PASS=admin123 \
  rabbitmq:3.8-management

# 2. 启动应用
cd capital-core && mvn spring-boot:run
```
